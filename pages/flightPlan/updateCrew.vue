<template>
  <view class="update-crew">
    <!-- 自定义导航栏 -->
    <CustomerNav title="修改人员" />

    <view class="form-box">
      <!--      <view class="form-box-title">修改人员</view>-->

      <!-- 使用公共人员修改组件 -->
      <CrewModify
        ref="crewModify"
        type="update"
        :initial-data="crewData"
        :multi-select-fields="multiSelectFields"
        @data-change="onCrewDataChange"
      />
      <view class="submit-btn-box">
        <van-button type="info" @click="submitForm">确认修改</van-button>
      </view>
    </view>
    <Background />
  </view>
</template>
<script>
import { assignCrew, listUserByRole, queryCrew } from '../../api/flightTask'
import { SUCCESS_CODE } from '../../utils/constant'
import Background from '../../components/Background/index.vue'
import CustomerNav from '../../components/CutomerNav/index.vue'
import CrewModify from '../../components/CrewModify/index.vue'

export default {
  name: 'UpdateCrew',
  components: { CustomerNav, Background, CrewModify },
  data() {
    return {
      staffObj: {},
      taskId: '',
      roleType: '',
      crewData: {}, // 人员数据
      // 配置哪些字段支持多选
      multiSelectFields: ['mechanicId'],
      // 组件返回的数据
      formIdData: {},
      formNameData: {},
    }
  },

  mounted() {
    this.getPickerListData().then(() => {
      this.getData()
    })
  },
  onLoad: function (option) {
    this.taskId = option.taskId
  },
  methods: {
    //获取下拉的人员
    async getData() {
      const { response: res } = await queryCrew({
        flightPlanId: Number(this.taskId),
      })
      if (res.code === SUCCESS_CODE && res.data) {
        this.crewData = res.data
      }
    },

    //获取下拉人员数据
    async getPickerListData() {
      const { response: res } = await listUserByRole()
      if (res.code === SUCCESS_CODE) {
        // this.pickerList = res.data
        const val = res.data

        if (val.deptType) {
          this.roleType = val.deptType.join(',')
          const userMap = { ...val.userMap }
          for (let key in userMap) {
            if (!userMap[key]) return
            userMap[key] = val.userMap[key].map((item) => {
              return {
                ...item,
                userId: String(item.userId) || '',
              }
            })
          }
          this.staffObj = {
            ...userMap,
            pilotType1: [
              { userName: '机长', userId: '机长' },
              { userName: '实习机长', userId: '实习机长' },
              { userName: '教员', userId: '教员' },
            ],
            pilotType2: [
              { userName: '副驾驶', userId: '副驾驶' },
              { userName: '副驾驶A', userId: '副驾驶A' },
              { userName: '学员', userId: '学员' },
              { userName: '同乘', userId: '同乘' },
            ],
          }
        }
      }
    },

    // 处理组件数据变化
    onCrewDataChange(data) {
      this.formIdData = data.formIdData
      this.formNameData = data.formNameData
    },
    async submitForm() {
      const params = {
        ...this.formIdData,
        flightPlanId: Number(this.taskId),
      }
      for (let key in params) {
        if (
          params[key] === undefined ||
          params[key] === '' ||
          params[key] === null
        ) {
          delete params[key]
        }
      }
      const { response } = await assignCrew(params)
      if (response.code === SUCCESS_CODE) {
        uni.showToast({
          title: response.msg || '操作成功',
          icon: 'success',
        })
        setTimeout(() => {
          // this.$emit('listenToChildEvent')
          uni.navigateBack()
        }, 1000)
      }
    },
  },
}
</script>

<style scoped lang="scss">
.form-box {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  //margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  box-sizing: border-box;
  width: calc(100% - 32px);
  margin: 16px auto;
}

.form-box-title {
  font-weight: bold;
  font-size: 16px;
  text-align: center;
  margin-bottom: 16px;
}



.submit-btn-box {
  width: 100%;
  margin: 16px auto;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.flex-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 6px;
}
</style>
