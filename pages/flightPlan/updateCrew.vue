<template>
  <view class="update-crew">
    <!-- 自定义导航栏 -->
    <CustomerNav title="修改人员" />

    <view class="form-box">
      <!--      <view class="form-box-title">修改人员</view>-->

      <view v-if="roleType.indexOf('1') > -1">
        <view class="section-title">飞行部</view>
        <van-row>
          <van-col span="14">
            <FormItem label="机长：" label-width="70px">
              <input
                class="input-box"
                v-model="formNameData.crewRole1"
                placeholder="请选择"
                disabled
                @click="openPicker('机长类型', 'pilotType1', 'crewRole1')"
              />
            </FormItem>
          </van-col>
          <van-col span="10">
            <FormItem label-width="0px">
              <input
                class="input-box"
                v-model="formNameData.captainId"
                placeholder="请选择"
                disabled
                @click="openPicker('机长', 'pilot', 'captainId')"
              />
            </FormItem>
          </van-col>
        </van-row>
        <van-row>
          <van-col span="14">
            <FormItem label="副机长：" label-width="70px">
              <input
                v-model="formNameData.crewRole2"
                placeholder="请选择"
                disabled
                @click="openPicker('副驾驶类型', 'pilotType2', 'crewRole2')"
              />
            </FormItem>
          </van-col>
          <van-col span="10">
            <FormItem label-width="0px">
              <input
                v-model="formNameData.copilotId"
                placeholder="请选择"
                disabled
                @click="openPicker('副机长', 'pilot', 'copilotId')"
              />
            </FormItem>
          </van-col>
        </van-row>
      </view>

      <view v-if="roleType.indexOf('2') > -1">
        <view class="section-title">机务部</view>
        <FormItem label="放行：" label-width="70px">
          <input
            v-model="formNameData.maintenanceId"
            placeholder="请选择"
            disabled
            @click="openPicker('放行', 'maintenance', 'maintenanceId')"
          />
        </FormItem>
        <FormItem label="机械员：" label-width="70px">
          <input
            v-model="formNameData.mechanicId"
            placeholder="请选择"
            disabled
            @click="openPicker('机械员', 'maintenance', 'mechanicId')"
          />
        </FormItem>
        <!--        <FormItem label="保障员：" label-width="70px">-->
        <!--          <input-->
        <!--            v-model="formNameData.safetyOfficerId"-->
        <!--            placeholder="请选择"-->
        <!--            disabled-->
        <!--            @click="openPicker('保障员', 'maintenance', 'safetyOfficerId')"-->
        <!--          />-->
        <!--        </FormItem>-->
      </view>

      <view v-if="roleType.indexOf('3') > -1">
        <view class="section-title">运控中心</view>
        <FormItem label="安检：" label-width="70px">
          <input
            v-model="formNameData.inspectorId"
            placeholder="请选择"
            disabled
            @click="openPicker('安检', 'operationControl', 'inspectorId')"
          />
        </FormItem>
        <FormItem label="现场组织：" label-width="70px">
          <input
            v-model="formNameData.organizationId"
            placeholder="请选择"
            disabled
            @click="
              openPicker('现场组织', 'operationControl', 'organizationId')
            "
          />
        </FormItem>
        <FormItem label="责任运控：" label-width="70px">
          <input
            v-model="formNameData.ocUserId"
            placeholder="请选择"
            disabled
            @click="openPicker('责任运控', 'operationControl', 'ocUserId')"
          />
        </FormItem>
        <FormItem label="运控助理：" label-width="70px">
          <input
            v-model="formNameData.ocAssistantId"
            placeholder="请选择"
            disabled
            @click="openPicker('运控助理', 'operationControl', 'ocAssistantId')"
          />
        </FormItem>
        <FormItem label="值班经理：" label-width="70px">
          <input
            v-model="formNameData.dutyManagerId"
            placeholder="请选择"
            disabled
            @click="openPicker('值班经理', 'operationControl', 'dutyManagerId')"
          />
        </FormItem>
      </view>

      <view v-if="roleType.indexOf('4') > -1">
        <view class="section-title">市场部</view>
        <FormItem label="售票：" label-width="70px">
          <input
            v-model="formNameData.conductorId"
            placeholder="请选择"
            disabled
            @click="openPicker('售票', 'conductor', 'conductorId')"
          />
        </FormItem>
      </view>
      <view class="submit-btn-box">
        <van-button type="info" @click="submitForm">确认修改</van-button>
      </view>
    </view>
    <Background />
    <van-popup
      :show="pickerData.show"
      position="bottom"
      :lock-scroll="true"
      custom-style="max-height: 60vh;"
      :root-portal="true"
    >
      <view class="popup-header">
        <text class="cancel" @click="closePicker">取消</text>
        <text class="title">{{ pickerData.title }}</text>
        <text class="confirm" @click="confirmPicker">确认</text>
      </view>
      <view class="popup-content">
        <!-- 多选组件 -->
        <van-checkbox-group v-if="isMultiSelect" :value="formCheckedIdData">
          <van-cell-group>
            <van-cell
              v-for="(item, index) in pickerData.list"
              :key="index"
              :title="item.userName"
              clickable
              @click="onRowChange(item.userId)"
            >
              <van-checkbox
                slot="right-icon"
                shape="square"
                :name="item.userId"
                :value="item.userId"
              />
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>

        <!-- 单选组件 -->
        <van-radio-group v-else :value="formCheckedIdData" :max="1">
          <van-cell
            v-for="(item, index) in pickerData.list"
            :key="index"
            :title="item.userName"
            clickable
            @click="onRowChange(item.userId)"
          >
            <van-radio
              slot="right-icon"
              :name="item.userId"
              :value="item.userId"
            />
          </van-cell>
        </van-radio-group>
      </view>
    </van-popup>
  </view>
</template>
<script>
import { assignCrew, listUserByRole, queryCrew } from '../../api/flightTask'
import { SUCCESS_CODE } from '../../utils/constant'
import Background from '../../components/Background/index.vue'
import CustomerNav from '../../components/CutomerNav/index.vue'
import FormItem from '../flightTask/compoents/FormItem.vue'

export default {
  name: 'UpdateCrew',
  components: { CustomerNav, Background, FormItem },
  data() {
    return {
      // formCheckedNameData: [],
      formCheckedIdData: '', //选择id的数据
      //下拉选择数据
      pickerData: {
        show: false,
        title: '',
        list: [],
        formKey: '',
      },
      formNameData: {}, //表单名称数据展示用
      formIdData: {}, //表单id数据
      staffObj: {},
      taskId: '',
      roleType: '',
      // 配置哪些字段支持多选
      multiSelectFields: ['mechanicId'],
    }
  },
  computed: {
    // 判断当前字段是否支持多选
    isMultiSelect() {
      return this.multiSelectFields.includes(this.pickerData.formKey)
    },
  },
  mounted() {
    this.getPickerListData().then(() => {
      this.getData()
    })
  },
  onLoad: function (option) {
    this.taskId = option.taskId
  },
  methods: {
    //获取下拉的人员
    async getData() {
      const { response: res } = await queryCrew({
        flightPlanId: Number(this.taskId),
      })
      if (res.code === SUCCESS_CODE && res.data) {
        const data = res.data
        this.getUserName(data)
        this.getUserIds(data)
      }
    },
    //处理人员数据回显示
    getUserName(newVal) {
      this.formNameData = {
        ...newVal,
        crewRole1: newVal.pilotType,
        crewRole2: newVal.copilotType,
        captainId: newVal.captain,
        copilotId: newVal.copilot,
        maintenanceId: newVal.maintenance,
        mechanicId: newVal.mechanic,
        inspectorId: newVal.inspector,
        organizationId: newVal.organization,
        ocUserId: newVal.ocUser,
        ocAssistantId: newVal.ocAssistant,
        dutyManagerId: newVal.dutyManager,
        conductorId: newVal.conductor,
      }
    },
    //处理人员id回显
    getUserIds(newVal) {
      this.formIdData = {
        ...newVal,
        captainId: this.transformPickerIdData(data.captain, 'pilot'),
        copilotId: this.transformPickerIdData(data.copilot, 'pilot'),
        conductorId: this.transformPickerIdData(data.conductor, 'conductor'),
        inspectorId: this.transformPickerIdData(
          data.inspector,
          'operationControl'
        ),
        mechanicId: this.transformPickerIdData(data.mechanic, 'maintenance'),
        maintenanceId: this.transformPickerIdData(
          data.maintenance,
          'maintenance'
        ),
        ocAssistantId: this.transformPickerIdData(
          data.ocAssistant,
          'operationControl'
        ),
        ocUserId: this.transformPickerIdData(data.ocUser, 'operationControl'),
        organizationId: this.transformPickerIdData(
          data.organization,
          'operationControl'
        ),
        dutyManagerId: this.transformPickerIdData(
          data.dutyManager,
          'operationControl'
        ),
      }
    },

    //获取下拉人员数据
    async getPickerListData() {
      const { response: res } = await listUserByRole()
      if (res.code === SUCCESS_CODE) {
        // this.pickerList = res.data
        const val = res.data

        if (val.deptType) {
          this.roleType = val.deptType.join(',')
          const userMap = { ...val.userMap }
          for (let key in userMap) {
            if (!userMap[key]) return
            userMap[key] = val.userMap[key].map((item) => {
              return {
                ...item,
                userId: String(item.userId) || '',
              }
            })
          }
          this.staffObj = {
            ...userMap,
            pilotType1: [
              { userName: '机长', userId: '机长' },
              { userName: '实习机长', userId: '实习机长' },
              { userName: '教员', userId: '教员' },
            ],
            pilotType2: [
              { userName: '副驾驶', userId: '副驾驶' },
              { userName: '副驾驶A', userId: '副驾驶A' },
              { userName: '学员', userId: '学员' },
              { userName: '同乘', userId: '同乘' },
            ],
          }
        }
      }
    },

    openPicker(title, listKey, formKey) {
      this.pickerData = {
        show: true,
        title: title,
        list: this.staffObj[listKey],
        formKey: formKey,
      }

      // 根据是否支持多选初始化数据
      const currentValue = this.formIdData[formKey]
      const isMultiSelectField = this.multiSelectFields.includes(formKey)

      if (isMultiSelectField) {
        // 多选字段：初始化为数组
        if (currentValue) {
          this.formCheckedIdData =
            typeof currentValue === 'string'
              ? currentValue.split(',').filter(Boolean)
              : [currentValue]
        } else {
          this.formCheckedIdData = []
        }
      } else {
        // 单选字段：初始化为字符串
        this.formCheckedIdData = currentValue || ''
      }
    },
    closePicker() {
      this.pickerData = {
        show: false,
        title: '',
        list: [],
        formKey: '',
      }
      this.$emit('modalShow', false)
      this.formCheckedIdData = []
    },
    //根据选中的id展示名字
    transformPickerData(id, key) {
      if (id && key) {
        // 支持单个和多个人员的解析
        const ids =
          typeof id === 'string' ? id.split(',').filter(Boolean) : [id]
        const names = ids
          .map((singleId) => {
            const user =
              this.staffObj[key] &&
              this.staffObj[key].find((item) => item.userId == singleId)
            return user ? user.userName : ''
          })
          .filter(Boolean)
        return names.join(', ')
      }
      return ''
    },
    //根据名字回填id
    transformPickerIdData(name, key) {
      if (name && key) {
        // 支持单个和多个人员的解析
        const names =
          typeof name === 'string' ? name.split(',').filter(Boolean) : [name]
        const ids = names
          .map((singleId) => {
            const user =
              this.staffObj[key] &&
              this.staffObj[key].find((item) => item.userName == singleId)
            return user ? user.userId : ''
          })
          .filter(Boolean)
        return ids.join(', ')
      }
      return ''
    },
    confirmPicker() {
      const isMultiSelectField = this.multiSelectFields.includes(
        this.pickerData.formKey
      )

      if (isMultiSelectField) {
        // 多选字段处理
        if (this.formCheckedIdData && this.formCheckedIdData.length > 0) {
          // 将数组转换为逗号分隔的字符串
          this.formIdData[this.pickerData.formKey] =
            this.formCheckedIdData.join(',')
          // 获取选中人员的名称
          const selectedNames = this.formCheckedIdData
            .map((id) => {
              const user = this.pickerData.list.find(
                (item) => item.userId == id
              )
              return user ? user.userName : ''
            })
            .filter(Boolean)
          this.formNameData[this.pickerData.formKey] = selectedNames.join(', ')
        }
      } else {
        // 单选字段处理
        if (this.formCheckedIdData) {
          this.formIdData[this.pickerData.formKey] = this.formCheckedIdData
          this.formNameData[this.pickerData.formKey] =
            this.pickerData.list.find(
              (item) => item.userId == this.formCheckedIdData
            )?.userName || ''
        }
      }
      this.closePicker()
    },
    onChange(event) {
      this.formCheckedIdData = event.detail
    },
    onRowChange(data) {
      const isMultiSelectField = this.multiSelectFields.includes(
        this.pickerData.formKey
      )

      if (isMultiSelectField) {
        // 多选
        const index = this.formCheckedIdData.indexOf(data)
        if (index > -1) {
          this.formCheckedIdData.splice(index, 1)
        } else {
          this.formCheckedIdData.push(data)
        }
      } else {
        // 单选
        this.formCheckedIdData = data
      }
    },
    async submitForm(id) {
      const params = {
        ...this.formIdData,
        flightPlanId: id,
      }
      for (let key in params) {
        if (
          params[key] === undefined ||
          params[key] === '' ||
          params[key] === null
        ) {
          delete params[key]
        }
      }
      const { response } = await assignCrew(params)
      if (response.code === SUCCESS_CODE) {
        uni.showToast({
          title: response.msg || '操作成功',
          icon: 'success',
        })
        setTimeout(() => {
          // this.$emit('listenToChildEvent')
          uni.navigateBack()
        }, 1000)
      }
    },
  },
}
</script>

<style scoped lang="scss">
.form-box {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  //margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  box-sizing: border-box;
  width: calc(100% - 32px);
  margin: 16px auto;
}

.form-box-title {
  font-weight: bold;
  font-size: 16px;
  text-align: center;
  margin-bottom: 16px;
}

.section-title {
  //font-weight: bold;
  font-size: 14px;
  padding: 12px 0 0 8px;
  position: relative;
  color: #2c5de5;

  &::before {
    content: '';
    display: block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #2c5de5;
    //background: #333;
    position: absolute;
    //left: -6px;
    left: 0;
    //top: calc(50% + 2px);
    top: 50%;
    transform: translateY(50%);
  }
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  box-sizing: border-box;

  .cancel {
    color: #999;
    font-size: 12px;
  }

  .confirm {
    color: #2c5de5;
    font-size: 12px;
  }

  .title {
    font-size: 14px;
    font-weight: bold;
    color: #333;
  }
}

.submit-btn-box {
  width: 100%;
  margin: 16px auto;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.flex-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 6px;
}
</style>
<style lang="scss">
.popup-content {
  padding: 16px;
  box-sizing: border-box;
  max-height: 80%;
  overflow-y: auto;
}
</style>
